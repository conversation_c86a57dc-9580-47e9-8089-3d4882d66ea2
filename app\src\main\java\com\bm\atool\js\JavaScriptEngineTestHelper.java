package com.bm.atool.js;

import android.content.Context;
import android.util.Log;

/**
 * JavaScript引擎测试辅助类
 * 用于验证JavaScript引擎修复是否有效
 */
public class JavaScriptEngineTestHelper {
    private static final String TAG = "JSEngineTestHelper";
    
    /**
     * 测试JavaScript引擎初始化
     * @param context Android上下文
     * @return 测试结果
     */
    public static TestResult testJavaScriptEngineInitialization(Context context) {
        JavaScriptEngine jsEngine = null;
        try {
            Log.i(TAG, "开始测试JavaScript引擎初始化...");
            
            // 尝试创建JavaScript引擎
            jsEngine = new JavaScriptEngine(context, null);
            
            if (!jsEngine.isAvailable()) {
                return new TestResult(false, "JavaScript引擎初始化后不可用");
            }
            
            // 测试基本的JavaScript执行
            JavaScriptEngine.ScriptResult result = jsEngine.executeScript("1 + 1");
            if (!result.success) {
                return new TestResult(false, "基本JavaScript执行失败: " + result.error);
            }
            
            if (!"2".equals(result.result)) {
                return new TestResult(false, "JavaScript计算结果错误，期望: 2, 实际: " + result.result);
            }
            
            Log.i(TAG, "JavaScript引擎初始化测试成功");
            return new TestResult(true, "JavaScript引擎初始化成功，基本功能正常");
            
        } catch (Exception e) {
            Log.e(TAG, "JavaScript引擎初始化测试失败", e);
            return new TestResult(false, "JavaScript引擎初始化异常: " + e.getMessage());
        } finally {
            if (jsEngine != null) {
                jsEngine.close();
            }
        }
    }
    
    /**
     * 测试Android桥接功能
     * @param context Android上下文
     * @return 测试结果
     */
    public static TestResult testAndroidBridge(Context context) {
        JavaScriptEngine jsEngine = null;
        try {
            Log.i(TAG, "开始测试Android桥接功能...");
            
            jsEngine = new JavaScriptEngine(context, null);
            
            // 测试Android对象是否存在
            JavaScriptEngine.ScriptResult result = jsEngine.executeScript("typeof Android");
            if (!result.success || !"object".equals(result.result)) {
                return new TestResult(false, "Android桥接对象不存在或类型错误");
            }
            
            // 测试console.log功能
            result = jsEngine.executeScript("console.log('测试日志'); 'console_test_ok'");
            if (!result.success || !"console_test_ok".equals(result.result)) {
                return new TestResult(false, "console.log功能测试失败");
            }
            
            // 测试Android.getCurrentTimestamp功能
            result = jsEngine.executeScript("Android.getCurrentTimestamp()");
            if (!result.success) {
                return new TestResult(false, "Android.getCurrentTimestamp()调用失败: " + result.error);
            }
            
            try {
                // 验证返回的是字符串格式的时间戳
                if (result.result == null || result.result.trim().isEmpty()) {
                    return new TestResult(false, "时间戳为空");
                }

                // 去掉可能的引号
                String timestampStr = result.result.replaceAll("\"", "");
                long timestamp = Long.parseLong(timestampStr);
                if (timestamp <= 0) {
                    return new TestResult(false, "时间戳值无效: " + timestamp);
                }
            } catch (NumberFormatException e) {
                return new TestResult(false, "时间戳格式错误: " + result.result);
            }
            
            Log.i(TAG, "Android桥接功能测试成功");
            return new TestResult(true, "Android桥接功能正常");
            
        } catch (Exception e) {
            Log.e(TAG, "Android桥接功能测试失败", e);
            return new TestResult(false, "Android桥接测试异常: " + e.getMessage());
        } finally {
            if (jsEngine != null) {
                jsEngine.close();
            }
        }
    }
    
    /**
     * 测试系统API功能
     * @param context Android上下文
     * @return 测试结果
     */
    public static TestResult testSystemAPIs(Context context) {
        JavaScriptEngine jsEngine = null;
        try {
            Log.i(TAG, "开始测试系统API功能...");
            
            jsEngine = new JavaScriptEngine(context, null);
            
            // 测试System对象是否存在
            JavaScriptEngine.ScriptResult result = jsEngine.executeScript("typeof System");
            if (!result.success || !"object".equals(result.result)) {
                return new TestResult(false, "System对象不存在或类型错误");
            }
            
            // 测试获取设备信息
            result = jsEngine.executeScript("System.getDeviceInfo()");
            if (!result.success) {
                return new TestResult(false, "System.getDeviceInfo()调用失败: " + result.error);
            }
            
            if (result.result == null || !result.result.contains("{")) {
                return new TestResult(false, "设备信息格式错误: " + result.result);
            }
            
            // 测试获取电池电量
            result = jsEngine.executeScript("System.getBatteryLevel()");
            if (!result.success) {
                return new TestResult(false, "System.getBatteryLevel()调用失败: " + result.error);
            }
            
            Log.i(TAG, "系统API功能测试成功");
            return new TestResult(true, "系统API功能正常");
            
        } catch (Exception e) {
            Log.e(TAG, "系统API功能测试失败", e);
            return new TestResult(false, "系统API测试异常: " + e.getMessage());
        } finally {
            if (jsEngine != null) {
                jsEngine.close();
            }
        }
    }
    
    /**
     * 运行完整的JavaScript引擎测试套件
     * @param context Android上下文
     * @return 综合测试结果
     */
    public static TestResult runFullTestSuite(Context context) {
        Log.i(TAG, "开始运行JavaScript引擎完整测试套件...");
        
        // 测试引擎初始化
        TestResult initResult = testJavaScriptEngineInitialization(context);
        if (!initResult.success) {
            return new TestResult(false, "引擎初始化测试失败: " + initResult.message);
        }
        
        // 测试Android桥接
        TestResult bridgeResult = testAndroidBridge(context);
        if (!bridgeResult.success) {
            return new TestResult(false, "Android桥接测试失败: " + bridgeResult.message);
        }
        
        // 测试系统API
        TestResult apiResult = testSystemAPIs(context);
        if (!apiResult.success) {
            return new TestResult(false, "系统API测试失败: " + apiResult.message);
        }
        
        Log.i(TAG, "JavaScript引擎完整测试套件运行成功");
        return new TestResult(true, "所有测试通过，JavaScript引擎功能正常");
    }
    
    /**
     * 测试结果类
     */
    public static class TestResult {
        public final boolean success;
        public final String message;
        
        public TestResult(boolean success, String message) {
            this.success = success;
            this.message = message;
        }
        
        @Override
        public String toString() {
            return (success ? "✓ 成功" : "✗ 失败") + ": " + message;
        }
    }
}
