package com.bm.atool.js;

import android.content.Context;
import android.util.Log;

/**
 * QuickJS兼容性测试类
 * 用于测试QuickJS支持的数据类型和绑定方式
 */
public class QuickJSCompatibilityTest {
    private static final String TAG = "QuickJSCompatTest";
    
    /**
     * 测试QuickJS支持的基本数据类型
     */
    public static void testSupportedTypes(Context context) {
        Log.i(TAG, "开始测试QuickJS支持的数据类型...");
        
        try {
            // 创建一个简化的测试接口
            TestInterface testInterface = new TestImplementation();
            
            // 测试各种数据类型
            testInterface.testVoid();
            testInterface.testString("test");
            testInterface.testInt(42);
            testInterface.testBoolean(true);
            testInterface.testDouble(3.14);
            
            Log.i(TAG, "✅ 所有基本数据类型测试通过");
            
        } catch (Exception e) {
            Log.e(TAG, "❌ 数据类型测试失败", e);
        }
    }
    
    /**
     * 测试接口定义（只包含QuickJS支持的类型）
     */
    interface TestInterface {
        void testVoid();
        String testString(String input);
        int testInt(int input);
        boolean testBoolean(boolean input);
        double testDouble(double input);
    }
    
    /**
     * 测试接口实现
     */
    static class TestImplementation implements TestInterface {
        @Override
        public void testVoid() {
            Log.d(TAG, "testVoid() called");
        }
        
        @Override
        public String testString(String input) {
            Log.d(TAG, "testString() called with: " + input);
            return "processed: " + input;
        }
        
        @Override
        public int testInt(int input) {
            Log.d(TAG, "testInt() called with: " + input);
            return input * 2;
        }
        
        @Override
        public boolean testBoolean(boolean input) {
            Log.d(TAG, "testBoolean() called with: " + input);
            return !input;
        }
        
        @Override
        public double testDouble(double input) {
            Log.d(TAG, "testDouble() called with: " + input);
            return input * 2.0;
        }
    }
    
    /**
     * 测试QuickJS绑定
     */
    public static boolean testQuickJSBinding(Context context) {
        Log.i(TAG, "开始测试QuickJS绑定...");
        
        try {
            app.cash.quickjs.QuickJs quickJs = app.cash.quickjs.QuickJs.create();
            
            // 测试绑定简化的接口
            TestInterface testImpl = new TestImplementation();
            quickJs.set("Test", TestInterface.class, testImpl);
            
            // 测试基本JavaScript执行
            Object result = quickJs.evaluate("1 + 1");
            Log.i(TAG, "基本计算结果: " + result);
            
            // 测试接口调用
            result = quickJs.evaluate("Test.testString('hello')");
            Log.i(TAG, "接口调用结果: " + result);
            
            quickJs.close();
            
            Log.i(TAG, "✅ QuickJS绑定测试成功");
            return true;
            
        } catch (Exception e) {
            Log.e(TAG, "❌ QuickJS绑定测试失败", e);
            return false;
        }
    }
    
    /**
     * 运行完整的兼容性测试
     */
    public static boolean runCompatibilityTest(Context context) {
        Log.i(TAG, "========================================");
        Log.i(TAG, "开始QuickJS兼容性测试");
        Log.i(TAG, "========================================");
        
        // 测试基本数据类型
        testSupportedTypes(context);
        
        // 测试QuickJS绑定
        boolean bindingSuccess = testQuickJSBinding(context);
        
        Log.i(TAG, "========================================");
        if (bindingSuccess) {
            Log.i(TAG, "🎉 QuickJS兼容性测试全部通过");
        } else {
            Log.e(TAG, "❌ QuickJS兼容性测试失败");
        }
        Log.i(TAG, "========================================");
        
        return bindingSuccess;
    }
}
